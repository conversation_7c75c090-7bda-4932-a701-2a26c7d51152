{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/app/%28map%29/api/health/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\n\n// 모델별 health check URL 매핑\nconst HEALTH_CHECK_URLS = {\n  'Qwen3-4B': 'http://121.163.19.104:8005/health',\n  'Qwen3-14B': 'http://121.163.19.104:8002/health',\n} as const;\n\nconst TIMEOUT_DURATION = 5000; // 5초 타임아웃\n\nexport async function GET(request: NextRequest) {\n  const startTime = Date.now();\n\n  // URL에서 모델 ID 파라미터 추출\n  const { searchParams } = new URL(request.url);\n  const modelId = searchParams.get('modelId') as keyof typeof HEALTH_CHECK_URLS;\n\n  // 기본값은 Qwen3-4B\n  const healthCheckUrl = HEALTH_CHECK_URLS[modelId] || HEALTH_CHECK_URLS['Qwen3-4B'];\n\n  try {\n    const controller = new AbortController();\n    const timeoutId = setTimeout(() => controller.abort(), TIMEOUT_DURATION);\n\n\n    const response = await fetch(healthCheckUrl, {\n      method: 'GET',\n      signal: controller.signal,\n      headers: {\n        'Content-Type': 'application/json',\n      },\n    });\n\n    clearTimeout(timeoutId);\n    const responseTime = Date.now() - startTime;\n\n    if (response.ok) {\n      // 서버에서 받은 응답을 그대로 전달하거나, 간단한 상태 정보만 전달\n      const data = await response.text();\n      \n      return NextResponse.json({\n        status: 'healthy',\n        responseTime,\n        timestamp: new Date().toISOString(),\n        modelId: modelId || 'Qwen3-4B',\n        healthCheckUrl,\n        data: data || 'OK'\n      });\n    } else {\n      return NextResponse.json({\n        status: 'unhealthy',\n        responseTime,\n        timestamp: new Date().toISOString(),\n        modelId: modelId || 'Qwen3-4B',\n        healthCheckUrl,\n        error: `HTTP ${response.status}: ${response.statusText}`\n      }, { status: response.status });\n    }\n  } catch (error) {\n    const responseTime = Date.now() - startTime;\n    let errorMessage = 'Unknown error';\n    \n    if (error instanceof Error) {\n      if (error.name === 'AbortError') {\n        errorMessage = 'Request timeout';\n      } else if (error.message.includes('fetch')) {\n        errorMessage = 'Network error - Server may be offline';\n      } else {\n        errorMessage = error.message;\n      }\n    }\n\n    return NextResponse.json({\n      status: 'unhealthy',\n      responseTime,\n      timestamp: new Date().toISOString(),\n      modelId: modelId || 'Qwen3-4B',\n      healthCheckUrl,\n      error: errorMessage\n    }, { status: 503 });\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEA,0BAA0B;AAC1B,MAAM,oBAAoB;IACxB,YAAY;IACZ,aAAa;AACf;AAEA,MAAM,mBAAmB,MAAM,UAAU;AAElC,eAAe,IAAI,OAAoB;IAC5C,MAAM,YAAY,KAAK,GAAG;IAE1B,sBAAsB;IACtB,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;IAC5C,MAAM,UAAU,aAAa,GAAG,CAAC;IAEjC,gBAAgB;IAChB,MAAM,iBAAiB,iBAAiB,CAAC,QAAQ,IAAI,iBAAiB,CAAC,WAAW;IAElF,IAAI;QACF,MAAM,aAAa,IAAI;QACvB,MAAM,YAAY,WAAW,IAAM,WAAW,KAAK,IAAI;QAGvD,MAAM,WAAW,MAAM,MAAM,gBAAgB;YAC3C,QAAQ;YACR,QAAQ,WAAW,MAAM;YACzB,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEA,aAAa;QACb,MAAM,eAAe,KAAK,GAAG,KAAK;QAElC,IAAI,SAAS,EAAE,EAAE;YACf,uCAAuC;YACvC,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,OAAO,yOAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,QAAQ;gBACR;gBACA,WAAW,IAAI,OAAO,WAAW;gBACjC,SAAS,WAAW;gBACpB;gBACA,MAAM,QAAQ;YAChB;QACF,OAAO;YACL,OAAO,yOAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,QAAQ;gBACR;gBACA,WAAW,IAAI,OAAO,WAAW;gBACjC,SAAS,WAAW;gBACpB;gBACA,OAAO,CAAC,KAAK,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,SAAS,UAAU,EAAE;YAC1D,GAAG;gBAAE,QAAQ,SAAS,MAAM;YAAC;QAC/B;IACF,EAAE,OAAO,OAAO;QACd,MAAM,eAAe,KAAK,GAAG,KAAK;QAClC,IAAI,eAAe;QAEnB,IAAI,iBAAiB,OAAO;YAC1B,IAAI,MAAM,IAAI,KAAK,cAAc;gBAC/B,eAAe;YACjB,OAAO,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,UAAU;gBAC1C,eAAe;YACjB,OAAO;gBACL,eAAe,MAAM,OAAO;YAC9B;QACF;QAEA,OAAO,yOAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,QAAQ;YACR;YACA,WAAW,IAAI,OAAO,WAAW;YACjC,SAAS,WAAW;YACpB;YACA,OAAO;QACT,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF", "debugId": null}}]}