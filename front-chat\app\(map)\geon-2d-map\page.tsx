import { ChatMap } from "@/components/chat-map/chat-map";
import { cookies } from 'next/headers';
import { DEFAULT_MODEL_NAME, models } from '@/lib/ai/models';
import { generateUUID } from "@/lib/utils";
import type { Metadata } from "next";

export const metadata: Metadata = {
  title: "말로 만드는 지도",
  description: "AI와 대화하며 지도를 탐색하고 분석하세요. 자연어로 지리 정보를 검색하고 시각화할 수 있는 혁신적인 지도 서비스입니다.",
  keywords: ["지도", "AI 지도", "대화형 지도", "지리정보", "GIS", "자연어 검색", "지도 분석"],
  openGraph: {
    title: "말로 만드는 지도 | 업무지원(챗봇)",
    description: "AI와 대화하며 지도를 탐색하고 분석하세요. 자연어로 지리 정보를 검색하고 시각화할 수 있는 혁신적인 지도 서비스입니다.",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "말로 만드는 지도 | 업무지원(챗봇)",
    description: "AI와 대화하며 지도를 탐색하고 분석하세요. 자연어로 지리 정보를 검색하고 시각화할 수 있는 혁신적인 지도 서비스입니다.",
  },
};

export default async function AIPage() {

	const id = generateUUID();

	const cookieStore = await cookies();
	const modelIdFromCookie = cookieStore.get('model-id')?.value;

	const selectedModelId =
		models.find((model) => model.id === modelIdFromCookie)?.id ||
		DEFAULT_MODEL_NAME;

	return (
		<ChatMap
			id={id}
			initialMessages={[]}
			selectedModelId={selectedModelId}
			selectedVisibilityType="private"
			isReadOnly={false}
		/>
	)
}
