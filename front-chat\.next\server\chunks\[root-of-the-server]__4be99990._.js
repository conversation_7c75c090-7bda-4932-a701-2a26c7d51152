module.exports = {

"[project]/.next-internal/server/app/(map)/api/health/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/app/(map)/api/health/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$3_$40$opentelemetry$2b$_a8516a9e8193861921635ddb2ae04d26$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.3_@opentelemetry+_a8516a9e8193861921635ddb2ae04d26/node_modules/next/server.js [app-route] (ecmascript)");
;
// 모델별 health check URL 매핑
const HEALTH_CHECK_URLS = {
    'Qwen3-4B': 'http://**************:8005/health',
    'Qwen3-14B': 'http://**************:8002/health'
};
const TIMEOUT_DURATION = 5000; // 5초 타임아웃
async function GET(request) {
    const startTime = Date.now();
    // URL에서 모델 ID 파라미터 추출
    const { searchParams } = new URL(request.url);
    const modelId = searchParams.get('modelId');
    // 기본값은 Qwen3-4B
    const healthCheckUrl = HEALTH_CHECK_URLS[modelId] || HEALTH_CHECK_URLS['Qwen3-4B'];
    try {
        const controller = new AbortController();
        const timeoutId = setTimeout(()=>controller.abort(), TIMEOUT_DURATION);
        const response = await fetch(healthCheckUrl, {
            method: 'GET',
            signal: controller.signal,
            headers: {
                'Content-Type': 'application/json'
            }
        });
        clearTimeout(timeoutId);
        const responseTime = Date.now() - startTime;
        if (response.ok) {
            // 서버에서 받은 응답을 그대로 전달하거나, 간단한 상태 정보만 전달
            const data = await response.text();
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$3_$40$opentelemetry$2b$_a8516a9e8193861921635ddb2ae04d26$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                status: 'healthy',
                responseTime,
                timestamp: new Date().toISOString(),
                modelId: modelId || 'Qwen3-4B',
                healthCheckUrl,
                data: data || 'OK'
            });
        } else {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$3_$40$opentelemetry$2b$_a8516a9e8193861921635ddb2ae04d26$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                status: 'unhealthy',
                responseTime,
                timestamp: new Date().toISOString(),
                modelId: modelId || 'Qwen3-4B',
                healthCheckUrl,
                error: `HTTP ${response.status}: ${response.statusText}`
            }, {
                status: response.status
            });
        }
    } catch (error) {
        const responseTime = Date.now() - startTime;
        let errorMessage = 'Unknown error';
        if (error instanceof Error) {
            if (error.name === 'AbortError') {
                errorMessage = 'Request timeout';
            } else if (error.message.includes('fetch')) {
                errorMessage = 'Network error - Server may be offline';
            } else {
                errorMessage = error.message;
            }
        }
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$3_$40$opentelemetry$2b$_a8516a9e8193861921635ddb2ae04d26$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            status: 'unhealthy',
            responseTime,
            timestamp: new Date().toISOString(),
            modelId: modelId || 'Qwen3-4B',
            healthCheckUrl,
            error: errorMessage
        }, {
            status: 503
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__4be99990._.js.map