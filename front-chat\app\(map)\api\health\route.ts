import { NextRequest, NextResponse } from 'next/server';

// 모델별 health check URL 매핑
const HEALTH_CHECK_URLS = {
  'Qwen3-4B': 'http://121.163.19.104:8005/health',
  'Qwen3-14B': 'http://121.163.19.104:8002/health',
} as const;

const TIMEOUT_DURATION = 5000; // 5초 타임아웃

export async function GET(request: NextRequest) {
  const startTime = Date.now();

  // URL에서 모델 ID 파라미터 추출
  const { searchParams } = new URL(request.url);
  const modelId = searchParams.get('modelId') as keyof typeof HEALTH_CHECK_URLS;

  // 기본값은 Qwen3-4B
  const healthCheckUrl = HEALTH_CHECK_URLS[modelId] || HEALTH_CHECK_URLS['Qwen3-4B'];

  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), TIMEOUT_DURATION);


    const response = await fetch(healthCheckUrl, {
      method: 'GET',
      signal: controller.signal,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    clearTimeout(timeoutId);
    const responseTime = Date.now() - startTime;

    if (response.ok) {
      // 서버에서 받은 응답을 그대로 전달하거나, 간단한 상태 정보만 전달
      const data = await response.text();
      
      return NextResponse.json({
        status: 'healthy',
        responseTime,
        timestamp: new Date().toISOString(),
        modelId: modelId || 'Qwen3-4B',
        healthCheckUrl,
        data: data || 'OK'
      });
    } else {
      return NextResponse.json({
        status: 'unhealthy',
        responseTime,
        timestamp: new Date().toISOString(),
        modelId: modelId || 'Qwen3-4B',
        healthCheckUrl,
        error: `HTTP ${response.status}: ${response.statusText}`
      }, { status: response.status });
    }
  } catch (error) {
    const responseTime = Date.now() - startTime;
    let errorMessage = 'Unknown error';
    
    if (error instanceof Error) {
      if (error.name === 'AbortError') {
        errorMessage = 'Request timeout';
      } else if (error.message.includes('fetch')) {
        errorMessage = 'Network error - Server may be offline';
      } else {
        errorMessage = error.message;
      }
    }

    return NextResponse.json({
      status: 'unhealthy',
      responseTime,
      timestamp: new Date().toISOString(),
      modelId: modelId || 'Qwen3-4B',
      healthCheckUrl,
      error: errorMessage
    }, { status: 503 });
  }
}
